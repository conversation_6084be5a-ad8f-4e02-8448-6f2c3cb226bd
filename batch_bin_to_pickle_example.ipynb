import sys
import os
from pathlib import Path

from module.race_data_processor import RaceProcessor

import glob
import pandas as pd

years = ['2017']  # 必要な年を追加してください

output_dir = Path('output')
output_dir.mkdir(exist_ok=True)

processor = RaceProcessor()

for year in years:
    print(f'{year}年のbinファイルを処理中...')
    bin_dir = f"F:\\keiba__AI_2025\\data\\html\\race\\race_by_year\\{year}"
    bin_files = glob.glob(f'{bin_dir}\\*.bin')
    race_info_list = []
    race_results_list = []
    for bin_file in bin_files:
        info_df, results_df = processor.parse_race_html(html_path=bin_file)
        if not info_df.empty:
            race_info_list.append(info_df)
        if not results_df.empty:
            race_results_list.append(results_df)
    # 年ごとにまとめてDataFrame化
    race_info_df = pd.concat(race_info_list, ignore_index=True) if race_info_list else pd.DataFrame()
    race_results_df = pd.concat(race_results_list, ignore_index=True) if race_results_list else pd.DataFrame()
    # DataFrameが空でなければ保存
    if not race_info_df.empty:
        race_info_df.to_pickle(output_dir / f'race_info_{year}.pickle')
        print(f'  race_info_{year}.pickle を保存')
    if not race_results_df.empty:
        race_results_df.to_pickle(output_dir / f'race_results_{year}.pickle')
        print(f'  race_results_{year}.pickle を保存')
    print(f'{year}年のpickle保存完了\n')

print('全ての年の処理が完了しました。')

# # 各年の馬IDを抽出して保存
# all_horse_ids = set()
# horse_ids_by_year = {}

# for year in years:
#     # race_results_{year}.pickle からDataFrameを読み込み
#     df_path = output_dir / f"race_results_{year}.pickle"
#     if not df_path.exists():
#         print(f"{df_path} が存在しません。スキップします。")
#         continue
#     race_results_df = pd.read_pickle(df_path)
#     # horse_idカラムからユニークなIDを抽出
#     horse_ids = set(race_results_df["horse_id"].dropna().unique())
#     horse_ids_by_year[year] = horse_ids
#     all_horse_ids.update(horse_ids)
#     # 年ごとに保存
#     pd.DataFrame(sorted(horse_ids), columns=["horse_id"]).to_csv(output_dir / f"horse_ids_{year}.csv", index=False)
#     print(f"{year}年の馬ID数: {len(horse_ids)} を保存")

# # 全体の馬IDも保存
# pd.DataFrame(sorted(all_horse_ids), columns=["horse_id"]).to_csv(output_dir / "horse_ids_all.csv", index=False)
# print(f"全体の馬ID数: {len(all_horse_ids)} を保存")

import module.horse_processor
horse_processor = module.horse_processor.HorseProcessor()

from module.race_horse_targeted_processor import RaceHorseTargetedProcessor

# プロセッサ作成
processor = RaceHorseTargetedProcessor()

# 2024年のレースから馬IDを抽出し、馬情報を処理
result = processor.process_race_to_horses(
    year="2021",
    include_basic_info=True,
    include_results=True,
    parallel=True,
    max_workers=4,
    save_output=True
)

#   コマンドライン実行

#   # 2024年のデータを処理
#   python race_horse_targeted_processor.py --year 2024 --save --workers 4

#   # 特定レースID処理
#   python race_horse_targeted_processor.py --race-id 202406010101 --save

#   # 馬基本情報のみ処理
#   python race_horse_targeted_processor.py --year 2024 --no-results --save

race_info = pd.read_pickle(output_dir / "race_info_2021.pickle")
race_info

race_results = pd.read_pickle(output_dir / "race_results_2021.pickle")
race_results

horse_info = pd.read_pickle(output_dir / "race_horses_horse_info_2021.pickle")
horse_info

horse_results = pd.read_pickle(output_dir / "race_horses_horse_results_2021.pickle")
horse_results

